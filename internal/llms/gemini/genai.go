// Package gemini 提供基於 Google GenAI SDK 的 Gemini AI 服務實現
//
// 本實現使用官方 google.golang.org/genai SDK 替代了之前的手動 HTTP 客戶端方式，
// 提供更穩定、更高效的 AI 對話功能。主要特性包括：
//
// 1. 對話管理：使用 genai.Chat API 進行原生對話管理，替代了之前的 garray.Array 歷史記錄
// 2. Token 監控：實現粗略的 token 使用量估算，支持自動歷史總結以控制 token 消耗
// 3. 歷史管理：當 token 使用量接近閾值時，自動觸發對話歷史總結功能
// 4. 錯誤處理：統一的錯誤映射機制，將 genai SDK 錯誤轉換為 GoFrame 錯誤類型
// 5. 附件支持：支持圖片、文件、YouTube 連結等多種附件類型的處理
// 6. 代理配置：支持 HTTP 代理配置，適應不同的網絡環境
// 7. 線程安全：利用 genai.Chat 的內建線程安全機制，無需額外的同步控制
//
// 使用示例：
//
//	gemini := New()
//	err := gemini.Initialize(ctx, config, payload)
//	response, err := gemini.Chat(ctx, message)
package gemini

import (
	"brainHub/internal/consts"
	"brainHub/internal/llms"
	"brainHub/internal/model"
	"brainHub/internal/model/llm"
	"context"
	"crypto/tls"
	"fmt"
	"net/http"
	"net/url"
	"os"
	"path/filepath"
	"strings"
	"time"

	"github.com/gabriel-vasile/mimetype"
	"github.com/gogf/gf/v2/encoding/gbase64"
	"github.com/gogf/gf/v2/encoding/gjson"
	"github.com/gogf/gf/v2/errors/gcode"
	"github.com/gogf/gf/v2/errors/gerror"
	"github.com/gogf/gf/v2/frame/g"
	"github.com/gogf/gf/v2/os/gfile"
	"github.com/gogf/gf/v2/os/glog"
	"github.com/gogf/gf/v2/os/gtime"
	"github.com/gogf/gf/v2/text/gstr"
	"github.com/gogf/gf/v2/util/gconv"
	"google.golang.org/genai"
)

// Gemini 相關常數
const (
	GeminiMaxRetryAttempts = 3                // 最大重試次數
	GeminiRetryDelaySecond = 2                // 重試延遲秒數
	GeminiHTTPTimeout      = 60 * time.Second // HTTP 請求超時時間
)

// Gemini Google Gemini AI 服務實現
// 透過 Google Vertex AI 和官方 genai SDK 提供安全、高效能的 AI 對話功能
type Gemini struct {
	genaiClient     *genai.Client // Google GenAI SDK 客戶端
	projectID       string        // GCP 項目 ID
	region          string        // GCP 區域
	modelName       string        // 模型名稱
	temperature     float32       // 生成溫度參數 (0.0-1.0)
	maxOutputTokens int32         // 最大輸出 token 數量
	topP            *float32      // Top-P 採樣參數 (0.0-1.0)
	topK            *float32      // Top-K 採樣參數
	genaiChat       *genai.Chat   // genai.Chat 實例，提供官方 SDK 的對話管理
	payload         *llm.Payload  // 初始化載荷

	// Token 使用量監控相關字段
	// 這些字段用於實現粗略的 token 使用量估算和自動歷史管理
	totalTokensUsed int32 // 累積使用的 token 數量（包括輸入和輸出）
	tokenThreshold  int32 // token 使用量閾值，超過時觸發歷史總結（默認 8000）
	estimatedTokens int32 // 當前對話的估算 token 數量（用於判斷是否需要總結）
}

// logger 返回專用的日誌記錄器
func (m *Gemini) logger() glog.ILogger {
	return g.Log().Cat(consts.CatalogGemini)
}

// New 創建新的 Gemini 服務實例
// 返回實現 ILLMs 接口的實例，使用 genai.Chat API 進行對話管理
func New() llms.ILLMs {
	return &Gemini{}
}

// Initialize 初始化 Gemini 服務
// 包含完整的參數驗證和錯誤處理
func (m *Gemini) Initialize(ctx context.Context, params *llm.LLMsConfig, payload *llm.Payload) (err error) {
	// 安全地記錄配置資訊
	m.logConfigSafely(ctx, params)

	// 參數驗證
	if params == nil {
		err = gerror.New("the params is nil")
		m.logger().Error(ctx, err)
		return
	}

	// 驗證 Vertex 配置
	if g.IsEmpty(params.Vertex.ProjectID) {
		err = gerror.New("vertex project id is required")
		m.logger().Error(ctx, err)
		return
	}
	if g.IsEmpty(params.Vertex.Region) {
		err = gerror.New("vertex region is required")
		m.logger().Error(ctx, err)
		return
	}
	if g.IsEmpty(params.Vertex.Gemini.Model) {
		err = gerror.New("gemini model name is required")
		m.logger().Error(ctx, err)
		return
	}

	// 設置基本配置
	m.projectID = params.Vertex.ProjectID
	m.region = params.Vertex.Region
	m.modelName = params.Vertex.Gemini.Model
	m.temperature = params.Vertex.Gemini.Temperature
	m.maxOutputTokens = params.Vertex.Gemini.MaxOutputTokens
	m.payload = payload

	// 設置進階生成參數（可選）
	m.setupAdvancedGenerationParams(ctx, params)
	// 解析憑證文件路徑
	credentialFile, err := m.resolveCredentialFilePath(ctx, params.Vertex.CredentialFile)
	if err != nil {
		m.logger().Error(ctx, err)
		return err
	}

	// 設置環境變量以支援 genai SDK 認證
	err = os.Setenv("GOOGLE_APPLICATION_CREDENTIALS", credentialFile)
	if err != nil {
		m.logger().Error(ctx, err)
		return err
	}
	m.logger().Infof(ctx, "Set GOOGLE_APPLICATION_CREDENTIALS: %s", credentialFile)

	// 初始化 genai 客戶端
	err = m.initializeGenAIClient(ctx, credentialFile)
	if err != nil {
		m.logger().Error(ctx, "Failed to initialize genai client:", err)
		return err
	}

	// 代理配置將在 initializeGenAIClient() 中處理
	m.logger().Debug(ctx, "Proxy configuration will be handled by genai SDK client")

	// genai SDK 會自動處理認證，不需要手動管理 access token

	// 初始化 token 使用量監控
	m.initializeTokenMonitoring(ctx)

	// 初始化 genai.Chat 實例
	err = m.initializeGenAIChat(ctx)
	if err != nil {
		m.logger().Error(ctx, "Failed to initialize genai.Chat:", err)
		return err
	}

	m.logger().Info(ctx, "Gemini service initialized successfully")
	return nil
}

// resolveCredentialFilePath 解析憑證文件路徑
// 統一處理絕對路徑和相對路徑的邏輯
func (m *Gemini) resolveCredentialFilePath(ctx context.Context, configuredPath string) (string, error) {
	if g.IsEmpty(configuredPath) {
		return "", gerror.New("credential file path is empty")
	}

	// 如果是絕對路徑，直接使用
	if filepath.IsAbs(configuredPath) {
		if !gfile.IsFile(configuredPath) {
			return "", gerror.Newf("credential file does not exist: %s", configuredPath)
		}
		return configuredPath, nil
	}

	// 如果是相對路徑，嘗試與 vertex_key_path 拼接
	vCredencialFilePath, err := g.Cfg().Get(ctx, "system.vertex_key_path", ".")
	if err != nil {
		return "", gerror.Wrap(err, "failed to get vertex_key_path config")
	}

	basePath := "."
	if vCredencialFilePath != nil && !vCredencialFilePath.IsEmpty() {
		basePath = vCredencialFilePath.String()
	}

	resolvedPath := gfile.Join(basePath, configuredPath)
	if !gfile.IsFile(resolvedPath) {
		return "", gerror.Newf("credential file does not exist: %s", resolvedPath)
	}

	return resolvedPath, nil
}

// setupAdvancedGenerationParams 設置進階生成參數
// 包含 TopP、TopK 等可選參數的配置
func (m *Gemini) setupAdvancedGenerationParams(ctx context.Context, params *llm.LLMsConfig) {
	m.logger().Debug(ctx, "Setting up advanced generation parameters")

	// 暫時使用默認值，未來可以從配置中讀取
	// TopP 控制核心採樣，值範圍 0.0-1.0，默認不設置讓 API 使用默認值
	// TopK 控制候選詞彙數量，默認不設置讓 API 使用默認值

	// 可以通過環境變量或配置文件設置這些參數
	vTopP, _ := g.Cfg().Get(ctx, "llm.gemini.top_p")
	if vTopP != nil && !vTopP.IsEmpty() {
		topPValue := vTopP.Float32()
		if topPValue > 0 && topPValue <= 1.0 {
			m.topP = &topPValue
			m.logger().Debugf(ctx, "TopP parameter set from config: %.2f", topPValue)
		}
	}

	vTopK, _ := g.Cfg().Get(ctx, "llm.gemini.top_k")
	if vTopK != nil && !vTopK.IsEmpty() {
		topKValue := vTopK.Float32()
		if topKValue > 0 {
			m.topK = &topKValue
			m.logger().Debugf(ctx, "TopK parameter set from config: %.0f", topKValue)
		}
	}

	if m.topP == nil && m.topK == nil {
		m.logger().Debug(ctx, "Using default TopP and TopK values from API")
	}

	m.logger().Debug(ctx, "Advanced generation parameters setup completed")
}

// logConfigSafely 安全地記錄配置資訊（隱藏敏感信息）
func (m *Gemini) logConfigSafely(ctx context.Context, params *llm.LLMsConfig) {
	if params == nil {
		return
	}

	safeConfig := map[string]interface{}{
		"project_id":        params.Vertex.ProjectID,
		"region":            params.Vertex.Region,
		"model":             params.Vertex.Gemini.Model,
		"temperature":       params.Vertex.Gemini.Temperature,
		"max_output_tokens": params.Vertex.Gemini.MaxOutputTokens,
		"credential_file":   "***masked***",
	}

	m.logger().Debugf(ctx, "Initializing Gemini with config: %v",
		gjson.New(safeConfig).MustToJsonIndentString())
}

// initializeGenAIClient 初始化 Google GenAI SDK 客戶端
// 使用官方 SDK 進行 Vertex AI 後端連接，支援代理配置
func (m *Gemini) initializeGenAIClient(ctx context.Context, credentialFile string) error {
	m.logger().Debug(ctx, "Initializing Google GenAI SDK client with Vertex AI backend")

	// 配置 genai 客戶端
	clientConfig := &genai.ClientConfig{
		Project:  m.projectID,
		Location: m.region,
		Backend:  genai.BackendVertexAI,
	}

	// 檢查是否需要設定代理
	vProxy, _ := g.Cfg().Get(ctx, "system.proxy")
	if vProxy != nil && !vProxy.IsEmpty() {
		proxyURL, parseErr := url.Parse(vProxy.String())
		if parseErr != nil {
			m.logger().Warningf(ctx, "Failed to parse proxy URL: %v", parseErr)
		} else {
			// 創建 HTTP Transport 配置
			transport := &http.Transport{
				Proxy: http.ProxyURL(proxyURL),
				// 添加連接池和超時配置以提高性能
				MaxIdleConns:        100,
				MaxIdleConnsPerHost: 10,
				IdleConnTimeout:     90 * time.Second,
				DisableCompression:  false,
			}

			// 檢查是否需要跳過 SSL 驗證（僅用於開發環境）
			vSkipSSL, _ := g.Cfg().Get(ctx, "system.proxy_skip_ssl", false)
			if vSkipSSL.Bool() {
				transport.TLSClientConfig = &tls.Config{InsecureSkipVerify: true}
				m.logger().Warning(ctx, "SSL verification disabled for proxy connection (development only)")
			}

			// 為 genai 客戶端設定代理（通過自定義 HTTP 客戶端）
			httpClient := &http.Client{
				Timeout:   GeminiHTTPTimeout,
				Transport: transport,
			}

			// 將自定義 HTTP 客戶端設定到 ClientConfig 中
			clientConfig.HTTPClient = httpClient
			m.logger().Infof(ctx, "Proxy configured for genai client: %s", proxyURL.String())
		}
	}

	// 創建 genai 客戶端（會自動使用 GOOGLE_APPLICATION_CREDENTIALS 環境變量進行認證）
	client, err := genai.NewClient(ctx, clientConfig)
	if err != nil {
		mappedErr := m.mapGenAIError(err)
		wrappedErr := gerror.Wrap(mappedErr, "failed to create genai client")
		m.logger().Error(ctx, wrappedErr)
		return wrappedErr
	}

	// 儲存客戶端實例
	m.genaiClient = client

	m.logger().Info(ctx, "Google GenAI SDK client initialized, authenticated, and proxy validated successfully")
	return nil
}

// initializeTokenMonitoring 初始化 token 使用量監控機制
// 設置 token 閾值和初始化計數器，為歷史記錄管理策略提供數據支持
func (m *Gemini) initializeTokenMonitoring(ctx context.Context) {
	m.logger().Debug(ctx, "Initializing token usage monitoring")

	// 重置 token 計數器
	m.totalTokensUsed = 0
	m.estimatedTokens = 0

	// 設置 token 閾值（默認為 maxOutputTokens 的 80%，確保有足夠空間處理響應）
	// 這個閾值可以根據實際需求調整
	if m.maxOutputTokens > 0 {
		m.tokenThreshold = int32(float32(m.maxOutputTokens) * 0.8)
	} else {
		// 如果沒有設置 maxOutputTokens，使用默認閾值
		m.tokenThreshold = 8000 // 默認閾值
	}

	m.logger().Debugf(ctx, "Token monitoring initialized - threshold: %d, maxOutput: %d",
		m.tokenThreshold, m.maxOutputTokens)
}

// estimateTokenCount 估算文本的 token 數量
// 使用簡單的啟發式方法：大約 4 個字符等於 1 個 token（英文），中文字符約 1.5 個字符等於 1 個 token
func (m *Gemini) estimateTokenCount(text string) int32 {
	if text == "" {
		return 0
	}

	// 計算字符數
	charCount := len([]rune(text))

	// 簡單的 token 估算：
	// - 英文和數字：約 4 個字符 = 1 token
	// - 中文字符：約 1.5 個字符 = 1 token
	// 使用保守估算，取較大值
	estimatedTokens := int32(float32(charCount) / 3.0) // 保守估算

	return estimatedTokens
}

// updateTokenUsage 更新 token 使用量統計
// 追蹤累積的 token 消耗，為歷史記錄管理策略提供數據
func (m *Gemini) updateTokenUsage(ctx context.Context, inputTokens, outputTokens int32) {
	m.totalTokensUsed += inputTokens + outputTokens
	m.estimatedTokens += inputTokens + outputTokens

	m.logger().Debugf(ctx, "Token usage updated - input: %d, output: %d, total: %d, estimated: %d",
		inputTokens, outputTokens, m.totalTokensUsed, m.estimatedTokens)
}

// shouldTriggerHistorySummary 檢查是否應該觸發歷史記錄總結
// 當預測 token 使用量即將超過閾值時返回 true
func (m *Gemini) shouldTriggerHistorySummary(ctx context.Context) bool {
	shouldTrigger := m.estimatedTokens >= m.tokenThreshold

	if shouldTrigger {
		m.logger().Debugf(ctx, "History summary should be triggered - estimated: %d, threshold: %d",
			m.estimatedTokens, m.tokenThreshold)
	}

	return shouldTrigger
}

// summarizeHistory 總結對話歷史記錄
// 當 token 使用量接近閾值時，將歷史對話壓縮成摘要以節省 token
//
// 工作流程：
// 1. 檢查 genai.Chat 實例是否已初始化
// 2. 使用特殊的總結提示詞請求 AI 生成對話摘要
// 3. 重新創建 genai.Chat 實例以清空歷史
// 4. 將摘要作為新的對話起點添加到歷史中
// 5. 重置 token 計數器
//
// 這種方式可以有效控制 token 使用量，避免超出模型的上下文限制
func (m *Gemini) summarizeHistory(ctx context.Context) error {
	m.logger().Debug(ctx, "Starting history summarization to manage token usage")

	if m.genaiChat == nil {
		return gerror.New("genai.Chat instance is not initialized")
	}

	// 獲取當前對話歷史
	history := m.genaiChat.History(false) // false 表示不包含系統消息
	if len(history) == 0 {
		m.logger().Debug(ctx, "No history to summarize")
		return nil
	}

	// 構建總結請求的內容
	summaryPrompt := "請將以上對話內容總結成簡潔的摘要，保留關鍵信息和上下文，以便後續對話參考。摘要應該：\n" +
		"1. 保留重要的事實和決定\n" +
		"2. 維持對話的邏輯脈絡\n" +
		"3. 盡可能簡潔，減少 token 使用量\n" +
		"4. 使用繁體中文回應"

	// 創建總結請求的 Part
	summaryPart := &genai.Part{
		Text: summaryPrompt,
	}

	// 發送總結請求
	response, err := m.genaiChat.SendMessage(ctx, *summaryPart)
	if err != nil {
		mappedErr := m.mapGenAIError(err)
		m.logger().Error(ctx, "Failed to generate history summary:", mappedErr)
		return gerror.Wrap(mappedErr, "failed to generate history summary")
	}

	// 提取總結內容
	var summaryText string
	if len(response.Candidates) > 0 && len(response.Candidates[0].Content.Parts) > 0 {
		if response.Candidates[0].Content.Parts[0].Text != "" {
			summaryText = response.Candidates[0].Content.Parts[0].Text
		}
	}

	if summaryText == "" {
		return gerror.New("failed to extract summary text from response")
	}

	m.logger().Debugf(ctx, "Generated history summary: %s", summaryText)

	// 重新創建 genai.Chat 實例以清空歷史
	err = m.initializeGenAIChat(ctx)
	if err != nil {
		return gerror.Wrap(err, "failed to reinitialize genai.Chat after summarization")
	}

	// 將總結作為上下文添加到新的對話中
	contextPart := &genai.Part{
		Text: fmt.Sprintf("對話歷史摘要：%s", summaryText),
	}

	_, err = m.genaiChat.SendMessage(ctx, *contextPart)
	if err != nil {
		mappedErr := m.mapGenAIError(err)
		m.logger().Error(ctx, "Failed to add summary context:", mappedErr)
		return gerror.Wrap(mappedErr, "failed to add summary context")
	}

	// 重置 token 估算計數器
	m.estimatedTokens = m.estimateTokenCount(summaryText)

	m.logger().Debugf(ctx, "History summarization completed, estimated tokens reset to: %d", m.estimatedTokens)
	return nil
}

// initializeGenAIChat 初始化 genai.Chat 實例
// 使用當前配置創建 Chat 實例，並從現有歷史記錄初始化對話狀態
func (m *Gemini) initializeGenAIChat(ctx context.Context) error {
	m.logger().Debug(ctx, "Initializing genai.Chat instance")

	// 確保 genai 客戶端已初始化
	if m.genaiClient == nil {
		return gerror.New("genai client is not initialized")
	}

	// 準備生成配置
	config := &genai.GenerateContentConfig{
		Temperature:     &m.temperature,
		MaxOutputTokens: m.maxOutputTokens,
	}

	// 設置可選參數
	if m.topP != nil {
		config.TopP = m.topP
	}
	if m.topK != nil {
		config.TopK = m.topK
	}

	// 創建 genai.Chat 實例（空歷史記錄）
	chat, err := m.genaiClient.Chats.Create(ctx, m.modelName, config, nil)
	if err != nil {
		mappedErr := m.mapGenAIError(err)
		return gerror.Wrap(mappedErr, "failed to create genai.Chat instance")
	}

	// 儲存 Chat 實例
	m.genaiChat = chat

	m.logger().Debug(ctx, "genai.Chat instance initialized successfully")
	return nil
}

// validateGenAIAuthentication 驗證 genai SDK 認證是否正常
// 通過嘗試列出可用模型來驗證認證狀態
// TODO: 暫時註釋掉整個方法，用於調試認證問題
/*
func (m *Gemini) validateGenAIAuthentication(ctx context.Context) error {
	m.logger().Debug(ctx, "Validating GenAI SDK authentication")

	if m.genaiClient == nil {
		return gerror.New("genai client is not initialized")
	}

	// 嘗試列出模型以驗證認證
	// 注意：這是一個輕量級的驗證操作，不會消耗大量資源
	listConfig := &genai.ListModelsConfig{}
	modelsPage, err := m.genaiClient.Models.List(ctx, listConfig)
	if err != nil {
		wrappedErr := gerror.Wrap(err, "failed to validate authentication by listing models")
		return wrappedErr
	}

	if len(modelsPage.Items) == 0 {
		return gerror.New("authentication validation failed: no models available")
	}

	m.logger().Debugf(ctx, "Authentication validated successfully, found %d available models", len(modelsPage.Items))
	return nil
}
*/

// validateProxyConfiguration 驗證代理配置是否正常工作
// 通過嘗試連接到 Google API 端點來測試代理連接
// func (m *Gemini) validateProxyConfiguration(ctx context.Context) error {
// 	m.logger().Debug(ctx, "Validating proxy configuration")

// 	// 檢查是否配置了代理
// 	vProxy, _ := g.Cfg().Get(ctx, "system.proxy")
// 	if vProxy == nil || vProxy.IsEmpty() {
// 		m.logger().Debug(ctx, "No proxy configured, skipping proxy validation")
// 		return nil
// 	}

// 	// 如果配置了代理，驗證代理連接是否正常
// 	// 這裡通過嘗試列出模型來間接驗證代理是否工作
// 	// 因為 genai SDK 會使用我們配置的 HTTP 客戶端（包含代理設定）
// 	if m.genaiClient != nil {
// 		listConfig := &genai.ListModelsConfig{}
// 		_, err := m.genaiClient.Models.List(ctx, listConfig)
// 		if err != nil {
// 			wrappedErr := gerror.Wrap(err, "proxy validation failed: unable to connect through proxy")
// 			m.logger().Error(ctx, wrappedErr)
// 			return wrappedErr
// 		}

// 		m.logger().Infof(ctx, "Proxy configuration validated successfully: %s", vProxy.String())
// 	}

// 	return nil
// }

// initializeAccessToken 方法已移除
// genai SDK 會自動處理 OAuth2 認證，不需要手動管理 access token

// refreshAccessToken 方法已移除
// genai SDK 會自動處理 token 刷新，不需要手動管理

// createNewChat 創建新的對話會話
// 使用 genai.Chat API 重新初始化對話實例，支持對話摘要和附件處理
func (m *Gemini) createNewChat(ctx context.Context, payload *llm.Payload, dialogSummary string) (err error) {
	m.logger().Debugf(ctx, "Create new chat with payload: %v, summary: %v",
		gjson.New(payload).MustToJsonIndentString(), dialogSummary)

	// 重新初始化 genai.Chat 實例以清空歷史
	err = m.initializeGenAIChat(ctx)
	if err != nil {
		m.logger().Error(ctx, "Failed to reinitialize genai.Chat:", err)
		return err
	}

	// 處理對話摘要，將其作為上下文添加到新對話中
	if !g.IsEmpty(dialogSummary) {
		summaryPart := &genai.Part{
			Text: fmt.Sprintf("對話歷史摘要：%s", dialogSummary),
		}

		_, err = m.genaiChat.SendMessage(ctx, *summaryPart)
		if err != nil {
			mappedErr := m.mapGenAIError(err)
			m.logger().Error(ctx, "Failed to add dialog summary:", mappedErr)
			return gerror.Wrap(mappedErr, "failed to add dialog summary to chat")
		}

		// 更新 token 估算
		summaryTokens := m.estimateTokenCount(dialogSummary)
		m.updateTokenUsage(ctx, summaryTokens, 0)

		m.logger().Debug(ctx, "Added conversation summary to chat history")
	}

	// 處理附件內容
	if payload != nil && payload.Attachments != nil {
		err = m.processAttachmentsForChat(ctx, payload.Attachments)
		if err != nil {
			m.logger().Error(ctx, "Failed to process attachments:", err)
			return gerror.Wrap(err, "failed to process attachments for chat")
		}
	}

	m.logger().Debug(ctx, "New chat session created successfully")
	return nil
}

// processAttachmentsForChat 處理附件內容並直接發送到 genai.Chat
// 將附件信息添加到對話歷史中作為上下文，支持圖片、文件、YouTube 等多種附件類型
func (m *Gemini) processAttachmentsForChat(ctx context.Context, attachments *model.Asset) error {
	m.logger().Debug(ctx, "Processing attachments for genai.Chat")

	if attachments == nil {
		m.logger().Debug(ctx, "No attachments to process")
		return nil
	}

	// 轉換附件為 genai.Part 格式
	parts, err := m.convertAssetToGenAIParts(ctx, attachments)
	if err != nil {
		return gerror.Wrap(err, "failed to convert attachments to genai.Part format")
	}

	if len(parts) == 0 {
		m.logger().Debug(ctx, "No valid attachment parts to process")
		return nil
	}

	// 將每個附件作為單獨的消息發送到 genai.Chat
	for i, part := range parts {
		_, err = m.genaiChat.SendMessage(ctx, *part)
		if err != nil {
			mappedErr := m.mapGenAIError(err)
			m.logger().Errorf(ctx, "Failed to send attachment part %d to chat: %v", i, mappedErr)
			return gerror.Wrapf(mappedErr, "failed to send attachment part %d to chat", i)
		}

		// 更新 token 估算（附件通常消耗較多 token）
		var tokenEstimate int32
		if part.Text != "" {
			tokenEstimate = m.estimateTokenCount(part.Text)
		} else {
			// 對於非文本附件，使用固定估算值
			tokenEstimate = 100 // 保守估算
		}
		m.updateTokenUsage(ctx, tokenEstimate, 0)

		m.logger().Debugf(ctx, "Successfully sent attachment part %d to chat", i)
	}

	m.logger().Debugf(ctx, "Successfully processed %d attachment parts for chat", len(parts))
	return nil
}

// isValidYouTubeURL 驗證 YouTube URL 格式
// 支援多種 YouTube URL 格式的驗證
func (m *Gemini) isValidYouTubeURL(urlStr string) bool {
	if g.IsEmpty(urlStr) {
		return false
	}

	// 支援的 YouTube URL 格式
	validPatterns := []string{
		"youtube.com/watch",
		"youtu.be/",
		"youtube.com/shorts/",
		"m.youtube.com/watch",
		"www.youtube.com/watch",
		"www.youtu.be/",
	}

	for _, pattern := range validPatterns {
		if strings.Contains(urlStr, pattern) {
			return true
		}
	}
	return false
}

// isSupportedImageType 檢查是否為支援的圖片類型
// Gemini 支援的圖片格式
func (m *Gemini) isSupportedImageType(mimeType string) bool {
	supportedTypes := []string{
		"image/jpeg",
		"image/jpg",
		"image/png",
		"image/gif",
		"image/webp",
		"image/heic",
		"image/heif",
	}

	for _, supportedType := range supportedTypes {
		if strings.EqualFold(mimeType, supportedType) {
			return true
		}
	}
	return false
}

// Chat 處理對話消息
// 使用 genai.Chat API 直接處理對話，集成 token 監控和歷史管理策略
func (m *Gemini) Chat(ctx context.Context, message *llm.Message) (*llm.ResponseData, error) {
	m.logger().Debugf(ctx, "Processing chat message: %v", gjson.New(message).MustToJsonIndentString())

	// 參數驗證
	if message == nil {
		err := gerror.New("message is nil")
		m.logger().Error(ctx, err)
		return nil, err
	}

	// 確保 genai.Chat 實例已初始化
	if m.genaiChat == nil {
		err := gerror.New("genai.Chat instance is not initialized")
		m.logger().Error(ctx, err)
		return nil, err
	}

	// 檢查是否需要觸發歷史總結
	if m.shouldTriggerHistorySummary(ctx) {
		m.logger().Debug(ctx, "Triggering history summarization due to token threshold")
		err := m.summarizeHistory(ctx)
		if err != nil {
			m.logger().Error(ctx, "Failed to summarize history:", err)
			// 不返回錯誤，繼續處理消息
		}
	}

	// 根據內容類型創建 genai.Part
	var messagePart *genai.Part
	var err error

	switch message.ContentType {
	case consts.ContentTypeText:
		// 處理文本消息
		messagePart = &genai.Part{
			Text: gconv.String(message.Content),
		}

	case consts.ContentMediaFile:
		// 處理媒體文件
		messagePart, err = m.processMediaMessageToPart(ctx, message)
		if err != nil {
			m.logger().Error(ctx, "Failed to process media message:", err)
			return nil, err
		}

	default:
		err = gerror.Newf("Unsupported content type: %v", message.ContentType)
		m.logger().Error(ctx, err)
		return nil, err
	}

	// 估算輸入 token 數量
	var inputTokens int32
	if messagePart.Text != "" {
		inputTokens = m.estimateTokenCount(messagePart.Text)
	} else {
		// 對於媒體文件，使用固定估算值
		inputTokens = 200 // 保守估算
	}

	// 發送消息到 genai.Chat
	response, err := m.genaiChat.SendMessage(ctx, *messagePart)
	if err != nil {
		mappedErr := m.mapGenAIError(err)
		m.logger().Error(ctx, "Failed to send message to genai.Chat:", mappedErr)
		return nil, gerror.Wrap(mappedErr, "failed to send message to genai.Chat")
	}

	// 提取響應文本
	var responseText string
	var outputTokens int32
	if len(response.Candidates) > 0 && len(response.Candidates[0].Content.Parts) > 0 {
		if response.Candidates[0].Content.Parts[0].Text != "" {
			responseText = response.Candidates[0].Content.Parts[0].Text
			outputTokens = m.estimateTokenCount(responseText)
		}
	}

	if responseText == "" {
		err = gerror.New("empty response from genai.Chat")
		m.logger().Error(ctx, err)
		return nil, err
	}

	// 更新 token 使用量統計
	m.updateTokenUsage(ctx, inputTokens, outputTokens)

	// 構建響應數據
	responseData := &llm.ResponseData{
		Response:        responseText,
		TotalTokenCount: m.totalTokensUsed,
	}

	m.logger().Debug(ctx, "Chat processing completed successfully")
	return responseData, nil
}

// processMediaMessageToPart 將媒體消息轉換為 genai.Part
// 支援圖片等媒體文件的處理
func (m *Gemini) processMediaMessageToPart(ctx context.Context, message *llm.Message) (*genai.Part, error) {
	data := gconv.Bytes(message.Content)
	mimeType := message.MimeType

	if g.IsEmpty(mimeType) {
		mimeType = http.DetectContentType(data)
	}

	// 創建 genai.Part 用於媒體文件
	part := &genai.Part{
		InlineData: &genai.Blob{
			MIMEType: mimeType,
			Data:     data,
		},
	}

	m.logger().Debugf(ctx, "Converted media message to genai.Part with MIME type: %s", mimeType)
	return part, nil
}

// clearHistory 清空對話歷史
// 通過重新創建 genai.Chat 實例來清空歷史，保持與現有接口的兼容性
func (m *Gemini) clearHistory(ctx context.Context) error {
	m.logger().Debug(ctx, "Clearing conversation history")

	// 重新初始化 genai.Chat 實例以清空歷史
	err := m.initializeGenAIChat(ctx)
	if err != nil {
		m.logger().Error(ctx, "Failed to reinitialize genai.Chat for history clearing:", err)
		return gerror.Wrap(err, "failed to clear history")
	}

	// 重置 token 計數器
	m.estimatedTokens = 0
	m.logger().Debug(ctx, "Token counters reset")

	m.logger().Debug(ctx, "Conversation history cleared successfully")
	return nil
}

// processMediaMessage 處理媒體消息
// 支援圖片等媒體文件的 base64 編碼處理
func (m *Gemini) processMediaMessage(ctx context.Context, message *llm.Message) (map[string]interface{}, error) {
	data := gconv.Bytes(message.Content)
	mimeType := message.MimeType

	if g.IsEmpty(mimeType) {
		mimeType = http.DetectContentType(data)
	}

	m.logger().Debugf(ctx, "Processing media message with MIME type: %s", mimeType)

	// 檢查是否為支援的圖片格式
	if !m.isSupportedImageType(mimeType) {
		return nil, gerror.Newf("Unsupported media type: %s", mimeType)
	}

	// 將圖片數據編碼為 base64
	base64Data := gbase64.EncodeToString(data)

	userMessage := map[string]interface{}{
		"role": "user",
		"parts": []map[string]interface{}{
			{
				"inline_data": map[string]interface{}{
					"mime_type": mimeType,
					"data":      base64Data,
				},
			},
			{
				"text": "請分析這個圖片",
			},
		},
	}

	return userMessage, nil
}

// convertAssetToGenAIParts 將 model.Asset 轉換為 genai.Part 格式
// 處理不同類型的附件（圖片、文件、YouTube、純文本等）
func (m *Gemini) convertAssetToGenAIParts(ctx context.Context, asset *model.Asset) ([]*genai.Part, error) {
	m.logger().Debug(ctx, "Converting model.Asset to genai.Part format")

	if asset == nil {
		return nil, nil
	}

	var parts []*genai.Part

	// 處理 YouTube 連結
	for i, youtubeLink := range asset.YoutubeLink {
		if !g.IsEmpty(youtubeLink) {
			part := &genai.Part{
				FileData: &genai.FileData{
					FileURI:  youtubeLink,
					MIMEType: "video/mp4",
				},
			}
			parts = append(parts, part)
			m.logger().Debugf(ctx, "Converted YouTube link %d to genai.Part", i)
		}
	}

	// 處理純文本
	for i, plainText := range asset.PlainText {
		if !g.IsEmpty(plainText) {
			part := &genai.Part{
				Text: fmt.Sprintf("純文本內容 #%d:\n%s", i+1, plainText),
			}
			parts = append(parts, part)
			m.logger().Debugf(ctx, "Converted plain text %d to genai.Part", i)
		}
	}

	// 處理網頁文件
	for i, webPageFile := range asset.WebPageFiles {
		if !g.IsEmpty(webPageFile) && gfile.IsFile(webPageFile) {
			content := gfile.GetContents(webPageFile)
			if !g.IsEmpty(content) {
				part := &genai.Part{
					Text: fmt.Sprintf("網頁內容文件 (%s):\n%s", gfile.Basename(webPageFile), content),
				}
				parts = append(parts, part)
				m.logger().Debugf(ctx, "Converted web page file %d to genai.Part", i)
			}
		}
	}

	// 處理一般文件
	for i, filePath := range asset.Files {
		if !g.IsEmpty(filePath) && gfile.IsFile(filePath) {
			// 檢查是否為圖片文件
			mimeType := mimetype.Detect(gfile.GetBytes(filePath))
			if mimeType != nil {
				mimeTypeStr := mimeType.String()

				// 處理圖片文件
				if gstr.HasPrefix(mimeTypeStr, "image/") {
					imageData := gfile.GetBytes(filePath)
					if len(imageData) > 0 {
						part := &genai.Part{
							InlineData: &genai.Blob{
								Data:     imageData,
								MIMEType: mimeTypeStr,
							},
						}
						parts = append(parts, part)
						m.logger().Debugf(ctx, "Converted image file %d to genai.Part", i)
						continue
					}
				}
			}

			// 處理非圖片文件（轉換為文本）
			content := gfile.GetContents(filePath)
			if !g.IsEmpty(content) {
				part := &genai.Part{
					Text: fmt.Sprintf("文件內容 (%s):\n%s", gfile.Basename(filePath), content),
				}
				parts = append(parts, part)
				m.logger().Debugf(ctx, "Converted file %d to genai.Part", i)
			}
		}
	}

	m.logger().Debugf(ctx, "Converted model.Asset to %d genai.Part objects", len(parts))
	return parts, nil
}

// mapGenAIError 映射 genai SDK 錯誤到 GoFrame 錯誤類型
// 確保錯誤處理的一致性和重試機制的正確運作
func (m *Gemini) mapGenAIError(err error) error {
	if err == nil {
		return nil
	}

	errStr := strings.ToLower(err.Error())

	// 映射認證錯誤（401, 403）
	if strings.Contains(errStr, "authentication") ||
		strings.Contains(errStr, "unauthorized") ||
		strings.Contains(errStr, "forbidden") ||
		strings.Contains(errStr, "credentials") ||
		strings.Contains(errStr, "permission denied") {
		return gerror.NewCode(gcode.CodeNotAuthorized, err.Error())
	}

	// 映射配額和速率限制錯誤（429）
	if strings.Contains(errStr, "quota") ||
		strings.Contains(errStr, "rate limit") ||
		strings.Contains(errStr, "too many requests") ||
		strings.Contains(errStr, "resource exhausted") {
		return gerror.NewCode(gcode.CodeOperationFailed, err.Error())
	}

	// 映射無效請求錯誤（400）
	if strings.Contains(errStr, "invalid") ||
		strings.Contains(errStr, "bad request") ||
		strings.Contains(errStr, "malformed") ||
		strings.Contains(errStr, "invalid_argument") {
		return gerror.NewCode(gcode.CodeInvalidParameter, err.Error())
	}

	// 映射服務不可用錯誤（503, 502, 504）
	if strings.Contains(errStr, "service unavailable") ||
		strings.Contains(errStr, "bad gateway") ||
		strings.Contains(errStr, "gateway timeout") ||
		strings.Contains(errStr, "unavailable") ||
		strings.Contains(errStr, "timeout") {
		return gerror.NewCode(gcode.CodeOperationFailed, err.Error())
	}

	// 映射內部服務器錯誤（500）
	if strings.Contains(errStr, "internal server error") ||
		strings.Contains(errStr, "internal error") {
		return gerror.NewCode(gcode.CodeInternalError, err.Error())
	}

	// 映射網絡連接錯誤
	if strings.Contains(errStr, "connection") ||
		strings.Contains(errStr, "network") ||
		strings.Contains(errStr, "dial") ||
		strings.Contains(errStr, "no such host") {
		return gerror.NewCode(gcode.CodeOperationFailed, err.Error())
	}

	// 映射內容過濾錯誤
	if strings.Contains(errStr, "content filter") ||
		strings.Contains(errStr, "safety") ||
		strings.Contains(errStr, "blocked") {
		return gerror.NewCode(gcode.CodeValidationFailed, err.Error())
	}

	// 其他錯誤使用通用包裝
	return gerror.Wrap(err, "genai SDK error")
}

// buildRequestBody 方法已移除
// genai SDK 會自動構建請求體，不需要手動實現

// getSystemInstruction 獲取系統指令
// 支援動態日期替換和指令格式化
func (m *Gemini) getSystemInstruction(ctx context.Context, payload *llm.Payload) string {
	if payload == nil {
		return ""
	}

	systemInstruction := payload.SystemInstruction
	if g.IsEmpty(systemInstruction) {
		return ""
	}

	// 替換動態日期
	systemInstruction = gstr.Replace(systemInstruction, "{{.now_date}}", gtime.Now().Format("Y-m-d"))

	m.logger().Debugf(ctx, "System instruction prepared: %s", systemInstruction)
	return systemInstruction
}

// GenerateContent 生成內容
// 實現內容生成接口，支援附件處理
func (m *Gemini) GenerateContent(ctx context.Context, request *llm.GenerateContentRequest) (*llm.GenerateContentResponse, error) {
	startTime := gtime.TimestampMilli()
	m.logger().Debugf(ctx, "Processing generate content request: %v", gjson.New(request).MustToJsonIndentString())

	// 參數驗證
	if request == nil {
		err := gerror.New("request is nil")
		m.logger().Error(ctx, err)
		return nil, err
	}

	if g.IsEmpty(request.Prompt) {
		err := gerror.New("prompt cannot be empty")
		m.logger().Error(ctx, err)
		return nil, err
	}

	// 應用預設值
	m.applyDefaults(ctx, request)

	// 記錄開始生成的日誌
	m.logger().Infof(ctx, "Starting Gemini GenerateContent: prompt_length=%d, max_continuations=%d, token_budget=%d",
		len(request.Prompt), request.MaxContinuations, request.TotalTokenBudget)

	// 初始化響應結構
	response := &llm.GenerateContentResponse{
		LLMName:           m.modelName,
		InputContent:      request.Prompt,
		ContinuationCount: 0,
		IsComplete:        false,
		SafetyWarnings:    make([]string, 0),
	}

	// 確保 genai.Chat 實例已初始化
	if m.genaiChat == nil {
		err := gerror.New("genai.Chat instance is not initialized")
		m.logger().Error(ctx, err)
		return nil, err
	}

	// 創建 genai.Part 用於提示詞
	promptPart := &genai.Part{
		Text: request.Prompt,
	}

	// 發送消息到 genai.Chat
	genaiResponse, err := m.genaiChat.SendMessage(ctx, *promptPart)
	if err != nil {
		mappedErr := m.mapGenAIError(err)
		m.logger().Error(ctx, "Failed to send message to genai.Chat:", mappedErr)
		return nil, gerror.Wrap(mappedErr, "failed to generate content using genai.Chat")
	}

	// 提取響應文本
	var responseText string
	var outputTokens int32
	if len(genaiResponse.Candidates) > 0 && len(genaiResponse.Candidates[0].Content.Parts) > 0 {
		if genaiResponse.Candidates[0].Content.Parts[0].Text != "" {
			responseText = genaiResponse.Candidates[0].Content.Parts[0].Text
			outputTokens = m.estimateTokenCount(responseText)
		}
	}

	if responseText == "" {
		err = gerror.New("empty response from genai.Chat")
		m.logger().Error(ctx, err)
		return nil, err
	}

	// 更新 token 使用量統計
	inputTokens := m.estimateTokenCount(request.Prompt)
	m.updateTokenUsage(ctx, inputTokens, outputTokens)

	// 更新響應數據
	response.OutputContent = responseText
	response.InputTokens = inputTokens
	response.OutputTokens = outputTokens
	response.TotalTokens = inputTokens + outputTokens
	response.IsComplete = true
	response.GenerationTime = gtime.TimestampMilli() - startTime

	m.logger().Debug(ctx, "Generate content completed successfully")
	return response, nil
}

// Release 釋放資源
// 清理歷史記錄、genai 客戶端和相關資源
func (m *Gemini) Release(ctx context.Context) {
	m.logger().Debug(ctx, "Releasing Gemini resources")

	// 清理 genai.Chat 實例
	if m.genaiChat != nil {
		m.genaiChat = nil
		m.logger().Debug(ctx, "GenAI Chat instance cleared")
	}

	// 清理 genai 客戶端引用
	if m.genaiClient != nil {
		// genai.Client 不需要顯式關閉，設為 nil 即可
		m.genaiClient = nil
		m.logger().Debug(ctx, "GenAI client reference cleared")
	}

	// genai SDK 客戶端會自動管理 HTTP 連接池
	m.logger().Debug(ctx, "HTTP resources managed by genai SDK")

	m.logger().Info(ctx, "Gemini resources released successfully")
}

// applyDefaults 應用預設配置值
// 為請求參數設置合理的預設值
func (m *Gemini) applyDefaults(ctx context.Context, request *llm.GenerateContentRequest) {
	if request.MaxContinuations <= 0 {
		request.MaxContinuations = 3 // 預設最大續寫次數
	}

	if request.TotalTokenBudget <= 0 {
		request.TotalTokenBudget = int32(m.maxOutputTokens * 2) // 預設 token 預算
	}

	if request.Temperature == nil {
		request.Temperature = &m.temperature // 使用實例的溫度設定
	}

	m.logger().Debugf(ctx, "Applied defaults: max_continuations=%d, token_budget=%d, temperature=%f",
		request.MaxContinuations, request.TotalTokenBudget, *request.Temperature)
}
